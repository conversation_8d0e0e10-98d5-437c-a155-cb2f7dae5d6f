#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号配置加载器
用于从 .env 文件中加载 Apple 账号配置信息
"""

import os
from typing import Dict, List, Tuple

def load_env_file(file_path: str = '.env') -> Dict[str, str]:
    """
    加载 .env 文件
    
    Args:
        file_path: .env 文件路径
        
    Returns:
        包含环境变量的字典
    """
    env_vars = {}
    
    if not os.path.exists(file_path):
        print(f"警告: {file_path} 文件不存在")
        return env_vars
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                
                # 跳过空行和注释行
                if not line or line.startswith('#'):
                    continue
                
                # 解析键值对
                if '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = value.strip()
                else:
                    print(f"警告: 第{line_num}行格式不正确: {line}")
    
    except Exception as e:
        print(f"读取 {file_path} 文件时出错: {e}")
    
    return env_vars

def get_account_info(account_id: int, env_vars: Dict[str, str]) -> Tuple[str, str, str]:
    """
    获取指定账号的信息
    
    Args:
        account_id: 账号ID (1-11)
        env_vars: 环境变量字典
        
    Returns:
        (邮箱, 密码, API地址) 的元组
    """
    email_key = f"ACCOUNT_{account_id}_EMAIL"
    password_key = f"ACCOUNT_{account_id}_PASSWORD"
    api_key = f"ACCOUNT_{account_id}_API_URL"
    
    email = env_vars.get(email_key, "")
    password = env_vars.get(password_key, "")
    api_url = env_vars.get(api_key, "")
    
    return email, password, api_url

def get_all_accounts(env_vars: Dict[str, str]) -> List[Dict[str, str]]:
    """
    获取所有账号信息
    
    Args:
        env_vars: 环境变量字典
        
    Returns:
        包含所有账号信息的列表
    """
    accounts = []
    total_accounts = int(env_vars.get('TOTAL_ACCOUNTS', 0))
    
    for i in range(1, total_accounts + 1):
        email, password, api_url = get_account_info(i, env_vars)
        
        if email and password and api_url:
            accounts.append({
                'id': i,
                'email': email,
                'password': password,
                'api_url': api_url
            })
    
    return accounts

def main():
    """主函数 - 演示如何使用"""
    print("=== Apple账号配置加载器 ===\n")
    
    # 加载环境变量
    env_vars = load_env_file()
    
    if not env_vars:
        print("没有找到有效的配置信息")
        return
    
    # 获取所有账号
    accounts = get_all_accounts(env_vars)
    
    print(f"共加载了 {len(accounts)} 个账号:\n")
    
    # 显示所有账号信息
    for account in accounts:
        print(f"账号 {account['id']}:")
        print(f"  邮箱: {account['email']}")
        print(f"  密码: {account['password']}")
        print(f"  API: {account['api_url']}")
        print()
    
    # 演示获取单个账号
    print("=" * 50)
    print("演示获取单个账号信息:")
    
    account_id = 1
    email, password, api_url = get_account_info(account_id, env_vars)
    print(f"账号 {account_id}:")
    print(f"  邮箱: {email}")
    print(f"  密码: {password}")
    print(f"  API: {api_url}")

if __name__ == '__main__':
    main()
