import random
import string

def generate_random_code(length=6):
    """
    生成随机六位数小写字母+数字混合的字符串
    
    Args:
        length (int): 生成字符串的长度，默认为6
    
    Returns:
        str: 随机生成的字符串
    """
    # 定义字符集：小写字母 + 数字
    characters = string.ascii_lowercase + string.digits
    
    # 随机选择指定长度的字符
    random_code = ''.join(random.choice(characters) for _ in range(length))
    
    return random_code

def generate_email_format(length=8):
    """
    生成邮箱格式的随机字符串: zhimeng2025 + 随机码 + @2925.com
    
    Args:
        length (int): 随机码的长度，默认为8
    
    Returns:
        str: 邮箱格式的字符串
    """
    random_code = generate_random_code(length)
    return f"{random_code}@91gmail.cn"

def generate_multiple_emails(count=1, length=6):
    """
    生成多个邮箱格式的随机字符串
    
    Args:
        count (int): 生成的数量
        length (int): 每个随机码的长度
    
    Returns:
        list: 邮箱格式字符串列表
    """
    return [generate_email_format(length) for _ in range(count)]

if __name__ == '__main__':
    print("随机邮箱地址生成器")
    print("格式: zhimeng2025[随机6位]+@91gmail.cn")
    print("=" * 50)
    
    # 生成单个随机邮箱
    single_email = generate_email_format()
    print(f"单个随机邮箱: {single_email}")
    
    print("-" * 50)
    
    # 生成多个随机邮箱
    multiple_emails = generate_multiple_emails(count=20)
    print("生成20个随机邮箱:")
    for i, email in enumerate(multiple_emails, 1):
        print(f"{i}. {email}")
    
    print("-" * 50)
    
    # 用户交互
    while True:
        try:
            user_input = input("\n请输入要生成的邮箱数量 (输入0退出): ")
            count = int(user_input)
            
            if count == 0:
                print("程序结束！")
                break
            elif count < 0:
                print("请输入正数！")
                continue
            
            emails = generate_multiple_emails(count)
            print(f"\n生成的{count}个随机邮箱:")
            for i, email in enumerate(emails, 1):
                print(f"{i}. {email}")
                
        except ValueError:
            print("请输入有效的数字！")
        except KeyboardInterrupt:
            print("\n\n程序被用户中断！")
            break
