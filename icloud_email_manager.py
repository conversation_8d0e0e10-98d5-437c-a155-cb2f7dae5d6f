"""
打包命令: 
pyinstaller iCloudEmailManager.spec

"""

import time
import sqlite3
import threading
import os
import json
import requests
import re
from datetime import datetime
from DrissionPage import Chromium, ChromiumOptions
import tkinter as tk
from tkinter import ttk, scrolledtext
from enum import Enum

class OperationType(Enum):
    """操作类型枚举"""
    CREATE = "生成"
    DELETE = "删除"
    DELETE_ALL = "全部删除"

class AppleEmailManager:
    """Apple隐藏邮件地址管理器"""

    # 类级别的数据库锁，所有实例共享
    _db_lock = threading.Lock()

    def __init__(self, thread_id=1, operation_type=OperationType.CREATE, ui_callback=None, status_callback=None, account_range="前10个"):
        self.thread_id = thread_id
        self.operation_type = operation_type
        self.ui_callback = ui_callback
        self.status_callback = status_callback
        self.account_range = account_range  # "前10个" 或 "后10个"
        self.TARGET_URL = "https://account.apple.com/account/manage/section/privacy"
        self.LOGIN_URL = "https://account.apple.com/sign-in"

        # 将数据库文件固定存储在桌面上
        import os
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        self.db_name = os.path.join(desktop_path, "apple_hide_emails.db")
        print(f"数据库路径: {self.db_name}")

        # 所有操作类型使用相同的端口范围，共用浏览器
        self.port_base = 1500  # 所有操作都使用1501-1520端口

        # 加载账号信息用于自动登录
        self.load_account_info()

        self.init_database()
        self.db_lock = AppleEmailManager._db_lock

        # 线程控制变量
        self.is_paused = threading.Event()
        self.is_paused.set()  # 默认为运行状态
        self.should_stop = threading.Event()
        self.email_count = 0

    def load_env_file(self, file_path='.env'):
        """加载.env文件"""
        env_vars = {}
        if not os.path.exists(file_path):
            print(f"警告: {file_path} 文件不存在")
            return env_vars

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    if '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key.strip()] = value.strip()
        except Exception as e:
            print(f"读取 {file_path} 文件时出错: {e}")

        return env_vars

    def load_account_info(self):
        """加载账号信息用于自动登录"""
        try:
            env_vars = self.load_env_file()

            # 根据账号范围选择计算实际的账号ID
            if self.account_range == "前10个":
                # 前10个账号：线程1->账号1, 线程2->账号2, ..., 线程10->账号10
                account_id = self.thread_id
            else:  # "后10个"
                # 后10个账号：线程1->账号11, 线程2->账号12, ..., 线程10->账号20
                account_id = self.thread_id + 10

            email_key = f"ACCOUNT_{account_id}_EMAIL"
            password_key = f"ACCOUNT_{account_id}_PASSWORD"
            api_key = f"ACCOUNT_{account_id}_API_URL"

            self.login_email = env_vars.get(email_key, "")
            self.login_password = env_vars.get(password_key, "")
            self.api_url = env_vars.get(api_key, "")

            if self.login_email and self.login_password and self.api_url:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 已加载{self.account_range}账号{account_id} {self.login_email}")
            else:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 账号{account_id}信息不完整，将无法自动登录")

        except Exception as e:
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 加载账号信息失败: {e}")
            self.login_email = ""
            self.login_password = ""
            self.api_url = ""

    def init_database(self):
        """初始化SQLite数据库"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()

            # 创建邮箱表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS emails (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT NOT NULL UNIQUE,
                    create_time TEXT NOT NULL
                )
            ''')
            
            # 创建已使用邮箱表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS 已使用 (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT NOT NULL UNIQUE,
                    use_time TEXT NOT NULL,
                    remark TEXT
                )
            ''')

            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"数据库初始化失败: {e}")

    def open_web(self, url=None):
        """打开网页"""
        port = self.port_base + self.thread_id
        print(f"[{self.operation_type.value}] 线程 {self.thread_id} 使用端口: {port}")
        
        co = ChromiumOptions().set_paths(local_port=port)
        self.page = Chromium(addr_or_opts=co)

        # 检查是否有已打开的标签页
        for t in self.page.get_tabs():
            if t.title != '新标签页':
                self.tab = t
                self.tab.wait.doc_loaded()
                return self.page, self.tab

        self.tab = self.page.latest_tab
        if url:
            self.tab.get(url=url)
        return self.page, self.tab

    def save_email_to_db(self, email):
        """保存邮箱到数据库"""
        with self.db_lock:
            try:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                cursor.execute(
                    "INSERT OR IGNORE INTO emails (email, create_time) VALUES (?, ?)",
                    (email, current_time)
                )

                if cursor.rowcount > 0:
                    self.email_count += 1
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 邮箱已保存 {email} [总计: {self.email_count}]")
                    conn.commit()
                    
                    if self.ui_callback:
                        self.ui_callback(self.thread_id, self.email_count, self.operation_type)
                    return True
                    
                conn.close()
                return False
            except Exception as e:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 保存邮箱失败 {e}")
                return False

    def delete_email_from_db(self, email, is_complete_delete=False):
        """从数据库删除邮箱"""
        with self.db_lock:
            try:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                if is_complete_delete:
                    # 彻底删除（从已使用表删除）
                    cursor.execute("DELETE FROM 已使用 WHERE email = ?", (email,))
                    remark = "彻底删除"
                else:
                    # 从emails表删除并移到已使用表
                    cursor.execute("DELETE FROM emails WHERE email = ?", (email,))
                    cursor.execute(
                        "INSERT OR IGNORE INTO 已使用 (email, use_time, remark) VALUES (?, ?, ?)",
                        (email, current_time, "已删除")
                    )
                    remark = "已删除"

                self.email_count += 1
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 邮箱{remark} {email} [总计: {self.email_count}]")
                conn.commit()
                conn.close()
                
                if self.ui_callback:
                    self.ui_callback(self.thread_id, self.email_count, self.operation_type)
                return True
                
            except Exception as e:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 删除邮箱失败 {e}")
                return False

    def get_used_emails_from_db(self):
        """从数据库获取已使用的邮箱列表"""
        with self.db_lock:
            try:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                cursor.execute("SELECT email FROM 已使用 ORDER BY use_time DESC")
                emails = [row[0] for row in cursor.fetchall()]
                conn.close()
                return emails
            except Exception as e:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 获取已使用邮箱失败 {e}")
                return []

    def get_verification_code_from_api(self):
        """从API获取验证码"""
        try:
            if not self.api_url:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: API地址未配置")
                return None

            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 正在从API获取验证码...")

            response = requests.get(self.api_url, timeout=30)
            response.raise_for_status()

            data = response.json()
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: API响应: {data}")

            if data.get('code') == 1 and 'data' in data:
                code_text = data['data'].get('code', '')

                # 使用正则表达式提取验证码（6位数字）
                match = re.search(r'\b(\d{6})\b', code_text)
                if match:
                    verification_code = match.group(1)
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 提取到验证码: {verification_code}")
                    return verification_code
                else:
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 无法从文本中提取验证码: {code_text}")
                    return None
            else:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: API返回错误: {data}")
                return None

        except Exception as e:
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 获取验证码失败: {e}")
            return None

    def auto_login(self):
        """执行自动登录流程"""
        try:
            if not all([self.login_email, self.login_password, self.api_url]):
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 账号信息不完整，无法自动登录")
                return False

            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 开始自动登录流程...")

            # 1. 输入邮箱
            selectors = [
                '#account_name_text_field',
                'input[can-field="accountName"]',
                'input[type="text"][autocomplete="off"]',
                '.form-textbox-input'
            ]

            email_input = None
            for selector in selectors:
                email_input = self.tab.ele(selector, timeout=3)
                if email_input:
                    break

            if not email_input:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 未找到邮箱输入框")
                return False

            email_input.clear()
            email_input.input(self.login_email)
            email_input.input('\n')  # 按回车
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 已输入邮箱并按回车")
            time.sleep(2)

            # 2. 输入密码
            password_selectors = [
                '#password_text_field',
                'input[type="password"]',
                'input[can-field="password"]'
            ]

            password_input = None
            for selector in password_selectors:
                password_input = self.tab.ele(selector, timeout=5)
                if password_input:
                    break

            if not password_input:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 未找到密码输入框")
                return False

            password_input.clear()
            password_input.input(self.login_password)
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 已输入密码")

            # 3. 点击登录按钮
            login_selectors = [
                '#sign-in',
                'button.signin-v2__buttons-wrapper__button-wrapper__button',
                'button[class*="signin"]'
            ]

            login_button = None
            for selector in login_selectors:
                login_button = self.tab.ele(selector, timeout=3)
                if login_button:
                    break

            if not login_button:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 未找到登录按钮")
                return False

            login_button.click()
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 已点击登录按钮")
            time.sleep(3)

            # 4. 处理验证码（如果需要）
            code_input = self.tab.ele('css:.form-security-code-input', timeout=10)
            if code_input:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 检测到验证码输入框")

                verification_code = self.get_verification_code_from_api()
                if verification_code:
                    code_input.input(verification_code)
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 已输入验证码")
                    time.sleep(3)
                else:
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 获取验证码失败")
                    return False

            # 5. 检查登录结果
            time.sleep(5)
            current_url = self.tab.url
            if not current_url.startswith(self.LOGIN_URL):
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 自动登录成功！")
                return True
            else:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 自动登录失败，仍在登录页面")
                return False

        except Exception as e:
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 自动登录过程出错: {e}")
            return False

    def check_email_box_full(self):
        """检查邮箱是否已满"""
        try:
            # 查找邮箱满的标志按钮
            full_btn = self.tab.ele("css:button.form-icons.form-icons-info19", timeout=2)
            if full_btn:
                span_text = full_btn.ele("css:span.visuallyhidden", timeout=1)
                if span_text and "创建新地址" in span_text.text:
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 检测到邮箱已满")
                    return True
            return False
        except:
            return False

    def find_and_click_specific_email(self, target_email):
        """在网页中查找并点击指定的邮箱"""
        try:
            # 查找所有邮箱卡片
            email_cards = self.tab.eles("css:div.card-line", timeout=3)
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 找到 {len(email_cards)} 个邮箱卡片")
            
            for card in email_cards:
                card_text = card.text.strip()
                if target_email in card_text:
                    card.click()
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 找到并点击邮箱 {target_email}")
                    time.sleep(1)  # 点击后等待
                    return True
            
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 未在网页中找到邮箱 {target_email}")
            return False
        except Exception as e:
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 查找邮箱时出错 {e}")
            return False

    def get_email_element(self):
        """获取邮箱元素（通用方法）"""
        try:
            email_element = self.tab.ele("css:.text.text-typography-body.text-color-glyph-gray.text-weight-regular.text-spacing-body", timeout=3)
            if email_element and email_element.text and "@icloud.com" in email_element.text:
                return email_element.text
            return None
        except Exception:
            return None

    def click_hide_email_card(self):
        """点击隐藏邮件地址卡片"""
        try:
            hide_email_btn = self.tab.ele("css:h3.card-title", timeout=1)
            if hide_email_btn and "隐藏邮件地址" in hide_email_btn.text:
                hide_email_btn.click()
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 成功点击隐藏邮件地址卡片，等待2秒")
                time.sleep(1)  # 点击后等待1秒
                return True
            return False
        except:
            return False

    def refresh_page(self):
        """刷新页面并等待，如果是登录页面则自动登录"""
        current_url = self.tab.url

        # 检查是否为登录页面
        if current_url.startswith(self.LOGIN_URL):
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 检测到登录页面，开始自动登录")
            if self.status_callback:
                self.status_callback(self.thread_id, "自动登录中", self.operation_type)

            # 执行自动登录
            login_success = self.auto_login()

            if login_success:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 自动登录成功，跳转到目标页面")
                if self.status_callback:
                    self.status_callback(self.thread_id, "登录成功", self.operation_type)
                # 登录成功后跳转到目标页面
                self.tab.get(self.TARGET_URL)
                time.sleep(5)
            else:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 自动登录失败，等待重试")
                if self.status_callback:
                    self.status_callback(self.thread_id, "登录失败", self.operation_type)
                time.sleep(10)  # 登录失败后等待更长时间再重试
            return

        # 正常刷新
        if self.status_callback:
            self.status_callback(self.thread_id, "正常", self.operation_type)

        print(f"[{self.operation_type.value}] 线程{self.thread_id}: 刷新页面并等待5秒")
        self.tab.get(self.TARGET_URL)
        time.sleep(5)

    def create_email_process(self):
        """生成邮箱流程"""
        if not self.click_hide_email_card():
            self.refresh_page()
            return

        # 检查邮箱是否已满
        if self.check_email_box_full():
            # 检查数据库中是否有可删除的邮箱
            used_emails = self.get_used_emails_from_db()
            if not used_emails:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 邮箱已满但无可删除邮箱，刷新页面等待")
                if self.status_callback:
                    self.status_callback(self.thread_id, "等待状态", self.operation_type)
                self.refresh_page()
                return
            else:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 邮箱已满，自动切换到删除模式")
                self.operation_type = OperationType.DELETE
                if self.status_callback:
                    self.status_callback(self.thread_id, "切换删除", self.operation_type)
                self.delete_email_process()
                return

        try:
            # 1. 点击"创建新地址"按钮（支持两种元素类型）
            create_btn = None
            
            # 方法1: 尝试查找icon-plus图标
            try:
                create_btn = self.tab.ele("css:.icon-plus", timeout=1)
                if create_btn:
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 找到icon-plus按钮")
                else:
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 未找到icon-plus按钮")
            except:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 查找icon-plus按钮时出错")
                pass
            
            # 方法2: 如果没找到，尝试查找div.card-title元素
            if not create_btn:
                try:
                    card_title_elements = self.tab.eles("css:div.card-title", timeout=1)
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 找到 {len(card_title_elements)} 个card-title元素")
                    
                    for element in card_title_elements:
                        element_text = element.text.strip()
                        print(f"[{self.operation_type.value}] 线程{self.thread_id}: card-title文本: '{element_text}'")
                        if "创建新地址" in element_text:
                            create_btn = element
                            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 找到匹配的card-title按钮")
                            break
                    
                    if not create_btn:
                        print(f"[{self.operation_type.value}] 线程{self.thread_id}: 未找到包含'创建新地址'的card-title元素")
                except Exception as e:
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 查找card-title时出错: {e}")
                    pass
            
            # 如果两种方法都没找到按钮，刷新页面
            if not create_btn:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 两种方法都未找到创建按钮，刷新页面")
                self.refresh_page()
                return
            
            create_btn.click()
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 成功点击创建新地址按钮")

            # 2. 输入文本
            text_input = self.tab.ele("css:.form-textbox-input", timeout=2)
            if not text_input:
                self.refresh_page()
                return
                
            text_input.clear()
            text_input.input("cursor")
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 成功输入文本")

            # 3. 点击创建按钮
            submit_btn = self.tab.ele("css:button.button.button-rounded-rectangle[type='submit']", timeout=2)
            if not submit_btn:
                self.refresh_page()
                return
                
            submit_btn.click()
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 成功点击创建按钮")
            time.sleep(2)

            # 4. 获取创建的邮箱
            email_address = self.get_email_element()
            if email_address:
                self.save_email_to_db(email_address)
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 邮箱创建成功 {email_address}")
            
            self.refresh_page()
            
        except Exception as e:
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 创建过程出错 {e}")
            self.refresh_page()

    def delete_email_process(self):
        """精确删除邮箱流程"""
        if not self.click_hide_email_card():
            self.refresh_page()
            return

        # 从数据库获取已使用的邮箱列表
        used_emails = self.get_used_emails_from_db()
        if not used_emails:
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 数据库中没有已使用的邮箱，自动切换到生成模式")
            self.operation_type = OperationType.CREATE
            if self.status_callback:
                self.status_callback(self.thread_id, "切换生成", self.operation_type)
            self.create_email_process()
            return

        try:
            # 1. 从已使用邮箱中选择一个进行删除
            target_email = used_emails[0]  # 选择最近使用的邮箱
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 准备精确删除邮箱 {target_email}")

            # 2. 在网页中查找并点击指定邮箱
            if not self.find_and_click_specific_email(target_email):
                # 如果在网页中找不到这个邮箱，从数据库中删除它
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 网页中未找到邮箱 {target_email}，从数据库中移除")
                self.delete_email_from_db(target_email, True)  # 彻底删除
                self.refresh_page()
                return

            # 3. 点击停用/删除按钮
            buttons = self.tab.eles("css:button.button-secondary", timeout=2)
            deactivate_btn = None

            for button in buttons:
                button_text = button.text.strip()
                if ("停用" in button_text and "电子邮件地址" in button_text) or ("删除" in button_text and "地址" in button_text):
                    deactivate_btn = button
                    break

            if not deactivate_btn:
                self.refresh_page()
                return

            button_text = deactivate_btn.text.strip()
            deactivate_btn.click()
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 成功点击 {button_text}")

            # 4. 点击最终确认按钮
            confirm_texts = ["删除", "停用"]
            confirm_btn = None

            for text in confirm_texts:
                btn = self.tab.ele(f"xpath://button[text()='{text}']", timeout=1)
                if btn:
                    confirm_btn = btn
                    break

            if not confirm_btn:
                buttons = self.tab.eles("css:button.button-secondary", timeout=1)
                for button in buttons:
                    if button.text.strip() in confirm_texts:
                        confirm_btn = button
                        break

            if not confirm_btn:
                self.refresh_page()
                return

            confirm_text = confirm_btn.text.strip()
            confirm_btn.click()
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 成功点击确认按钮 {confirm_text}")

            # 5. 从数据库中删除邮箱
            is_complete_delete = "删除" in confirm_text
            self.delete_email_from_db(target_email, is_complete_delete)

            self.refresh_page()

        except Exception as e:
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 删除过程出错 {e}")
            self.refresh_page()

    def delete_all_email_process(self):
        """全部删除邮箱流程 - 不从已使用表读取，直接删除网页上的所有邮箱"""
        if not self.click_hide_email_card():
            self.refresh_page()
            return

        try:
            # 1. 寻找并点击"来自"设置"
            try:
                # 尝试直接使用XPath查找包含特定文本的元素
                settings_element = self.tab.ele("xpath://div[contains(text(), '来自') and contains(text(), '设置')]", timeout=2)

                # 如果没找到，尝试查找所有卡片元素并筛选
                if not settings_element:
                    elements = self.tab.eles("css:div.card-right-element", timeout=2)
                    for element in elements:
                        if "来自" in element.text and "设置" in element.text:
                            settings_element = element
                            break

                # 如果找到了元素，点击它
                if settings_element:
                    settings_element.click()
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 成功点击\"来自\"设置\"")
                else:
                    # 如果没找到，刷新页面并等待
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 未找到\"来自\"设置\"元素，刷新页面")
                    self.refresh_page()
                    return
            except:
                # 出现任何错误，刷新页面并等待
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 点击\"来自\"设置\"时出错，刷新页面")
                self.refresh_page()
                return

            # 2. 寻找并点击"停用电子邮件地址"或"删除地址"按钮
            try:
                # 查找所有按钮元素
                buttons = self.tab.eles("css:button.button-secondary", timeout=2)

                # 遍历找到匹配的按钮
                deactivate_btn = None
                for button in buttons:
                    button_text = button.text.strip()
                    if ("停用" in button_text and "电子邮件地址" in button_text) or ("删除" in button_text and "地址" in button_text):
                        deactivate_btn = button
                        break

                # 如果找到了按钮，点击它
                if deactivate_btn:
                    button_text = deactivate_btn.text.strip()
                    deactivate_btn.click()
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 成功点击按钮: {button_text}")
                else:
                    # 如果没找到，刷新页面并等待
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 未找到停用/删除按钮，刷新页面")
                    self.refresh_page()
                    return
            except:
                # 出现任何错误，刷新页面并等待
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 查找停用/删除按钮时出错，刷新页面")
                self.refresh_page()
                return

            # 3. 获取被删除的邮箱地址
            try:
                email_address = self.get_email_element()
                if email_address:
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 获取邮箱: {email_address}")
                else:
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 未获取到邮箱地址，刷新页面")
                    self.refresh_page()
                    return
            except:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 获取邮箱地址时出错，刷新页面")
                self.refresh_page()
                return

            # 4. 点击最终确认按钮(停用或删除)
            try:
                confirm_btn = None
                confirm_texts = ["删除", "停用"]

                # 方法1: 使用XPath直接查找
                for text in confirm_texts:
                    btn = self.tab.ele(f"xpath://button[text()='{text}']", timeout=1)
                    if btn:
                        confirm_btn = btn
                        break

                # 方法2: 如果方法1失败，查找所有按钮并筛选
                if not confirm_btn:
                    buttons = self.tab.eles("css:button.button-secondary", timeout=1)
                    for button in buttons:
                        if button.text.strip() in confirm_texts:
                            confirm_btn = button
                            break

                # 如果找到了确认按钮，点击它
                if confirm_btn:
                    confirm_text = confirm_btn.text.strip()
                    confirm_btn.click()
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 成功点击确认按钮: {confirm_text}")
                else:
                    # 如果没找到，刷新页面并等待
                    print(f"[{self.operation_type.value}] 线程{self.thread_id}: 未找到确认按钮，刷新页面")
                    self.refresh_page()
                    return
            except:
                # 出现任何错误，刷新页面并等待
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 点击确认按钮时出错，刷新页面")
                self.refresh_page()
                return

            # 5. 处理数据库操作 - 全部删除模式只从emails表删除，不添加到已使用表
            try:
                # 从emails表删除邮箱（如果存在）
                with self.db_lock:
                    conn = sqlite3.connect(self.db_name)
                    cursor = conn.cursor()
                    cursor.execute("DELETE FROM emails WHERE email = ?", (email_address,))
                    conn.commit()
                    conn.close()

                self.email_count += 1
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 邮箱已删除 {email_address} [总计: {self.email_count}]")

                if self.ui_callback:
                    self.ui_callback(self.thread_id, self.email_count, self.operation_type)

                # 操作完成后刷新页面
                self.refresh_page()
            except:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 数据库操作时出错，刷新页面")
                self.refresh_page()

        except Exception as e:
            print(f"[{self.operation_type.value}] 线程{self.thread_id}: 全部删除过程出错 {e}")
            self.refresh_page()

    def run_process(self):
        """运行主流程"""
        while True:
            # 检查停止信号
            if self.should_stop.is_set():
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 收到停止信号，退出")
                break

            # 检查暂停信号
            if not self.is_paused.is_set():
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 已暂停，等待恢复...")
                self.is_paused.wait()
                if self.should_stop.is_set():
                    break
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 已恢复运行")

            try:
                if self.operation_type == OperationType.CREATE:
                    self.create_email_process()
                elif self.operation_type == OperationType.DELETE:
                    self.delete_email_process()
                else:  # DELETE_ALL
                    self.delete_all_email_process()

            except Exception as e:
                print(f"[{self.operation_type.value}] 线程{self.thread_id}: 主流程出错 {e}")
                self.refresh_page()


class EmailManagerUI:
    """邮件管理器UI界面"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("iCloud邮件管理器")
        self.root.geometry("1000x750")  # 增加高度以容纳新控件

        # 存储线程实例 - 所有操作类型共用同一个字典
        self.threads = {}  # 存储AppleEmailManager实例
        self.thread_objects = {}  # 存储Thread对象

        # 保留原有的分类字典以兼容现有代码
        self.create_threads = {}
        self.delete_threads = {}
        self.delete_all_threads = {}
        self.create_thread_objects = {}
        self.delete_thread_objects = {}
        self.delete_all_thread_objects = {}

        # 账号范围选择
        self.account_range = tk.StringVar(value="前10个")

        self.setup_ui()

    def setup_ui(self):
        """设置UI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="iCloud邮件管理器", font=("Arial", 18, "bold"))
        title_label.grid(row=0, column=0, columnspan=6, pady=(0, 10))

        # 账号选择区域
        account_frame = ttk.LabelFrame(main_frame, text="账号选择", padding="10")
        account_frame.grid(row=1, column=0, columnspan=6, pady=(0, 20), sticky=(tk.W, tk.E))

        ttk.Label(account_frame, text="选择使用的账号范围:", font=("Arial", 10)).grid(row=0, column=0, padx=(0, 10))

        # 账号范围选择单选按钮
        ttk.Radiobutton(account_frame, text="前10个账号 (账号1-10)", variable=self.account_range,
                       value="前10个", command=self.on_account_range_change).grid(row=0, column=1, padx=(0, 20))
        ttk.Radiobutton(account_frame, text="后10个账号 (账号11-20)", variable=self.account_range,
                       value="后10个", command=self.on_account_range_change).grid(row=0, column=2, padx=(0, 20))

        # 显示当前选择的账号信息
        self.account_info_label = ttk.Label(account_frame, text="", font=("Arial", 9), foreground="blue")
        self.account_info_label.grid(row=1, column=0, columnspan=3, pady=(10, 0))

        # 初始化显示账号信息
        self.update_account_info_display()

        # 控制区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=6, pady=(0, 20), sticky=(tk.W, tk.E))

        # 生成邮箱控制
        create_frame = ttk.LabelFrame(control_frame, text="生成邮箱", padding="10")
        create_frame.grid(row=0, column=0, padx=(0, 5), sticky=(tk.W, tk.E, tk.N, tk.S))

        ttk.Label(create_frame, text="线程数:").grid(row=0, column=0, padx=(0, 5))
        self.create_thread_var = tk.StringVar(value="1")
        create_entry = ttk.Entry(create_frame, textvariable=self.create_thread_var, width=8)
        create_entry.grid(row=0, column=1, padx=(0, 5))

        ttk.Button(create_frame, text="启动", command=lambda: self.start_threads(OperationType.CREATE)).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(create_frame, text="停止", command=lambda: self.stop_threads(OperationType.CREATE)).grid(row=0, column=3)

        # 删除邮箱控制
        delete_frame = ttk.LabelFrame(control_frame, text="删除邮箱", padding="10")
        delete_frame.grid(row=0, column=1, padx=(5, 5), sticky=(tk.W, tk.E, tk.N, tk.S))

        ttk.Label(delete_frame, text="线程数:").grid(row=0, column=0, padx=(0, 5))
        self.delete_thread_var = tk.StringVar(value="1")
        delete_entry = ttk.Entry(delete_frame, textvariable=self.delete_thread_var, width=8)
        delete_entry.grid(row=0, column=1, padx=(0, 5))

        ttk.Button(delete_frame, text="启动", command=lambda: self.start_threads(OperationType.DELETE)).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(delete_frame, text="停止", command=lambda: self.stop_threads(OperationType.DELETE)).grid(row=0, column=3)

        # 全部删除邮箱控制
        delete_all_frame = ttk.LabelFrame(control_frame, text="全部删除", padding="10")
        delete_all_frame.grid(row=0, column=2, padx=(5, 0), sticky=(tk.W, tk.E, tk.N, tk.S))

        ttk.Label(delete_all_frame, text="线程数:").grid(row=0, column=0, padx=(0, 5))
        self.delete_all_thread_var = tk.StringVar(value="1")
        delete_all_entry = ttk.Entry(delete_all_frame, textvariable=self.delete_all_thread_var, width=8)
        delete_all_entry.grid(row=0, column=1, padx=(0, 5))

        ttk.Button(delete_all_frame, text="启动", command=lambda: self.start_threads(OperationType.DELETE_ALL)).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(delete_all_frame, text="停止", command=lambda: self.stop_threads(OperationType.DELETE_ALL)).grid(row=0, column=3)

        # 统计显示区域
        stats_frame = ttk.LabelFrame(main_frame, text="线程状态统计", padding="10")
        stats_frame.grid(row=3, column=0, columnspan=6, pady=(0, 20), sticky=(tk.W, tk.E, tk.N, tk.S))

        # 创建三列
        left_frame = ttk.Frame(stats_frame)
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        middle_frame = ttk.Frame(stats_frame)
        middle_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 5))

        right_frame = ttk.Frame(stats_frame)
        right_frame.grid(row=0, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))

        # 生成邮箱统计
        ttk.Label(left_frame, text="生成邮箱线程", font=("Arial", 12, "bold")).grid(row=0, column=0, pady=(0, 10))
        self.create_canvas = tk.Canvas(left_frame, height=200)
        create_scrollbar = ttk.Scrollbar(left_frame, orient="vertical", command=self.create_canvas.yview)
        self.create_scrollable_frame = ttk.Frame(self.create_canvas)

        self.create_scrollable_frame.bind("<Configure>", lambda e: self.create_canvas.configure(scrollregion=self.create_canvas.bbox("all")))
        self.create_canvas.create_window((0, 0), window=self.create_scrollable_frame, anchor="nw")
        self.create_canvas.configure(yscrollcommand=create_scrollbar.set)

        self.create_canvas.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        create_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))

        # 删除邮箱统计
        ttk.Label(middle_frame, text="删除邮箱线程", font=("Arial", 12, "bold")).grid(row=0, column=0, pady=(0, 10))
        self.delete_canvas = tk.Canvas(middle_frame, height=200)
        delete_scrollbar = ttk.Scrollbar(middle_frame, orient="vertical", command=self.delete_canvas.yview)
        self.delete_scrollable_frame = ttk.Frame(self.delete_canvas)

        self.delete_scrollable_frame.bind("<Configure>", lambda e: self.delete_canvas.configure(scrollregion=self.delete_canvas.bbox("all")))
        self.delete_canvas.create_window((0, 0), window=self.delete_scrollable_frame, anchor="nw")
        self.delete_canvas.configure(yscrollcommand=delete_scrollbar.set)

        self.delete_canvas.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        delete_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))

        # 全部删除邮箱统计
        ttk.Label(right_frame, text="全部删除线程", font=("Arial", 12, "bold")).grid(row=0, column=0, pady=(0, 10))
        self.delete_all_canvas = tk.Canvas(right_frame, height=200)
        delete_all_scrollbar = ttk.Scrollbar(right_frame, orient="vertical", command=self.delete_all_canvas.yview)
        self.delete_all_scrollable_frame = ttk.Frame(self.delete_all_canvas)

        self.delete_all_scrollable_frame.bind("<Configure>", lambda e: self.delete_all_canvas.configure(scrollregion=self.delete_all_canvas.bbox("all")))
        self.delete_all_canvas.create_window((0, 0), window=self.delete_all_scrollable_frame, anchor="nw")
        self.delete_all_canvas.configure(yscrollcommand=delete_all_scrollbar.set)

        self.delete_all_canvas.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        delete_all_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=6, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=100)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        control_frame.columnconfigure(0, weight=1)
        control_frame.columnconfigure(1, weight=1)
        control_frame.columnconfigure(2, weight=1)
        stats_frame.columnconfigure(0, weight=1)
        stats_frame.columnconfigure(1, weight=1)
        stats_frame.columnconfigure(2, weight=1)
        left_frame.columnconfigure(0, weight=1)
        left_frame.rowconfigure(1, weight=1)
        middle_frame.columnconfigure(0, weight=1)
        middle_frame.rowconfigure(1, weight=1)
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime('%H:%M:%S')
        self.log_text.insert(tk.END, f"{timestamp} - {message}\n")
        self.log_text.see(tk.END)

    def on_account_range_change(self):
        """账号范围选择变更回调"""
        self.update_account_info_display()
        self.log_message(f"账号范围已切换到: {self.account_range.get()}")

    def update_account_info_display(self):
        """更新账号信息显示"""
        try:
            # 创建临时管理器实例来加载账号信息
            temp_manager = AppleEmailManager(thread_id=1, account_range=self.account_range.get())

            if self.account_range.get() == "前10个":
                info_text = f"当前选择: 前10个账号 (账号1-10) | 线程1将使用: {temp_manager.login_email}"
            else:
                info_text = f"当前选择: 后10个账号 (账号11-20) | 线程1将使用: {temp_manager.login_email}"

            self.account_info_label.config(text=info_text)

        except Exception as e:
            self.account_info_label.config(text=f"获取账号信息失败: {e}")

    def update_email_count(self, thread_id, count, operation_type):
        """更新邮箱计数"""
        try:
            if operation_type == OperationType.CREATE:
                prefix = "create"
                label_text = f"邮箱: {count}"
            elif operation_type == OperationType.DELETE:
                prefix = "delete"
                label_text = f"已删除: {count}"
            else:  # DELETE_ALL
                prefix = "delete_all"
                label_text = f"已删除: {count}"

            count_label = getattr(self, f"{prefix}_count_label_{thread_id}", None)
            if count_label:
                count_label.config(text=label_text)
        except Exception as e:
            print(f"更新UI计数时出错: {e}")

    def update_thread_status(self, thread_id, status, operation_type):
        """更新线程状态"""
        try:
            if operation_type == OperationType.CREATE:
                prefix = "create"
            elif operation_type == OperationType.DELETE:
                prefix = "delete"
            else:  # DELETE_ALL
                prefix = "delete_all"

            status_label = getattr(self, f"{prefix}_status_label_{thread_id}", None)
            if status_label:
                color_map = {
                    "正常": "green",
                    "等待登录": "orange",
                    "自动登录中": "blue",
                    "登录成功": "green",
                    "登录失败": "red",
                    "异常": "red",
                    "切换生成": "blue",
                    "切换删除": "purple",
                    "等待状态": "gray"
                }
                color = color_map.get(status, "black")
                status_label.config(text=status, foreground=color)
        except Exception as e:
            print(f"更新UI状态时出错: {e}")

    def create_thread_display(self, thread_id, operation_type):
        """创建线程显示控件"""
        if operation_type == OperationType.CREATE:
            parent = self.create_scrollable_frame
            prefix = "create"
            count_text = "邮箱: 0"
        elif operation_type == OperationType.DELETE:
            parent = self.delete_scrollable_frame
            prefix = "delete"
            count_text = "已删除: 0"
        else:  # DELETE_ALL
            parent = self.delete_all_scrollable_frame
            prefix = "delete_all"
            count_text = "已删除: 0"

        frame = ttk.Frame(parent)
        frame.grid(row=thread_id-1, column=0, pady=2, sticky=(tk.W, tk.E))

        # 线程标签
        ttk.Label(frame, text=f"线程 {thread_id}:", width=10).grid(row=0, column=0, padx=(0, 10))

        # 状态标签
        status_label = ttk.Label(frame, text="正常", width=10, foreground="green", font=("Arial", 9, "bold"))
        status_label.grid(row=0, column=1, padx=(0, 10))

        # 计数标签
        count_label = ttk.Label(frame, text=count_text, width=15, font=("Arial", 10, "bold"))
        count_label.grid(row=0, column=2)

        # 存储控件引用
        setattr(self, f"{prefix}_status_label_{thread_id}", status_label)
        setattr(self, f"{prefix}_count_label_{thread_id}", count_label)

    def start_threads(self, operation_type):
        """启动线程"""
        try:
            if operation_type == OperationType.CREATE:
                thread_count = int(self.create_thread_var.get())
                threads_dict = self.create_threads
                thread_objects_dict = self.create_thread_objects
                parent_frame = self.create_scrollable_frame
            elif operation_type == OperationType.DELETE:
                thread_count = int(self.delete_thread_var.get())
                threads_dict = self.delete_threads
                thread_objects_dict = self.delete_thread_objects
                parent_frame = self.delete_scrollable_frame
            else:  # DELETE_ALL
                thread_count = int(self.delete_all_thread_var.get())
                threads_dict = self.delete_all_threads
                thread_objects_dict = self.delete_all_thread_objects
                parent_frame = self.delete_all_scrollable_frame

            if thread_count < 1 or thread_count > 20:
                self.log_message(f"{operation_type.value}: 线程数应该在1-20之间")
                return

            self.log_message(f"{operation_type.value}: 准备启动 {thread_count} 个线程...")

            # 清理现有显示
            for widget in parent_frame.winfo_children():
                widget.destroy()

            # 启动新线程
            for i in range(1, thread_count + 1):
                # 检查是否已经存在相同线程ID的管理器（共用浏览器）
                if i in self.threads:
                    # 如果已存在，停止旧的操作并更新操作类型
                    old_manager = self.threads[i]
                    old_manager.should_stop.set()
                    time.sleep(0.5)  # 等待旧线程停止
                
                manager = AppleEmailManager(i, operation_type, self.update_email_count, self.update_thread_status, self.account_range.get())
                
                # 如果存在旧的管理器且有浏览器实例，复用浏览器
                if i in self.threads and hasattr(self.threads[i], 'page') and hasattr(self.threads[i], 'tab'):
                    try:
                        manager.page = self.threads[i].page
                        manager.tab = self.threads[i].tab
                        print(f"[{operation_type.value}] 线程{i}: 复用现有浏览器实例")
                    except:
                        # 如果复用失败，将在worker_thread中重新创建
                        pass
                
                # 更新所有相关字典
                self.threads[i] = manager
                threads_dict[i] = manager

                thread = threading.Thread(target=self.worker_thread, args=(i, manager))
                thread.daemon = True
                self.thread_objects[i] = thread
                thread_objects_dict[i] = thread
                thread.start()

                self.create_thread_display(i, operation_type)
                self.log_message(f"{operation_type.value}: 线程 {i} 已启动")
                time.sleep(0.5)  # 间隔启动

            self.log_message(f"{operation_type.value}: 所有 {thread_count} 个线程已启动完成")

        except ValueError:
            self.log_message(f"{operation_type.value}: 请输入有效的数字")
        except Exception as e:
            self.log_message(f"{operation_type.value}: 启动线程时出错 {e}")

    def stop_threads(self, operation_type):
        """停止线程"""
        if operation_type == OperationType.CREATE:
            threads_dict = self.create_threads
        elif operation_type == OperationType.DELETE:
            threads_dict = self.delete_threads
        else:  # DELETE_ALL
            threads_dict = self.delete_all_threads

        stopped_count = 0
        for thread_id, manager in threads_dict.items():
            # 只停止匹配当前操作类型的线程
            if manager.operation_type == operation_type:
                manager.should_stop.set()
                manager.is_paused.set()
                stopped_count += 1

        self.log_message(f"{operation_type.value}: {stopped_count} 个线程已停止")

    def worker_thread(self, thread_id, manager):
        """工作线程"""
        try:
            # 如果还没有浏览器实例，则创建新的
            if not hasattr(manager, 'page') or not hasattr(manager, 'tab'):
                manager.page, manager.tab = manager.open_web(manager.TARGET_URL)
                time.sleep(3)
            else:
                # 如果复用了浏览器，确保在正确的页面
                try:
                    manager.tab.get(manager.TARGET_URL)
                    time.sleep(2)
                    print(f"[{manager.operation_type.value}] 线程{thread_id}: 使用复用的浏览器实例")
                except:
                    # 如果复用的浏览器有问题，重新创建
                    manager.page, manager.tab = manager.open_web(manager.TARGET_URL)
                    time.sleep(3)
                    print(f"[{manager.operation_type.value}] 线程{thread_id}: 复用失败，重新创建浏览器实例")
                    
            manager.run_process()
        except Exception as e:
            self.log_message(f"{manager.operation_type.value}: 线程{thread_id} 出错 {e}")

    def run(self):
        """运行UI"""
        self.root.mainloop()


if __name__ == '__main__':
    try:
        print("=== iCloud邮件管理器 ===")
        print("启动UI界面...")
        
        # 导入os模块以获取桌面路径
        import os
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        print(f"桌面路径: {desktop_path}")
        
        # 初始化数据库
        manager = AppleEmailManager()
        
        ui = EmailManagerUI()
        ui.run()

    except Exception as e:
        print(f"程序运行出现错误: {e}")
        print("程序即将退出...") 