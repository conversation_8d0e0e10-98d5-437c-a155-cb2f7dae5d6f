#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Apple账号自动登录脚本
实现自动登录到Apple账户页面的完整流程
"""

import time
import json
import requests
import re
from DrissionPage import Chromium, ChromiumOptions
from load_accounts import load_env_file, get_account_info

class AppleAutoLogin:
    """Apple账号自动登录器"""
    
    def __init__(self, account_id=1):
        """
        初始化自动登录器
        
        Args:
            account_id: 要使用的账号ID (默认为1)
        """
        self.account_id = account_id
        self.TARGET_URL = "https://account.apple.com/sign-in"
        self.port = 1501  # 使用与主程序相同的端口范围
        
        # 加载账号信息
        self.load_account_info()
        
        # 初始化浏览器
        self.page = None
        self.tab = None
        
    def load_account_info(self):
        """从.env文件加载账号信息"""
        try:
            env_vars = load_env_file()
            self.email, self.password, self.api_url = get_account_info(self.account_id, env_vars)
            
            if not all([self.email, self.password, self.api_url]):
                raise ValueError(f"账号{self.account_id}信息不完整")
                
            print(f"已加载账号{self.account_id}: {self.email}")
            
        except Exception as e:
            print(f"加载账号信息失败: {e}")
            raise
    
    def open_browser(self):
        """打开浏览器并导航到登录页面"""
        try:
            print(f"使用端口: {self.port}")
            co = ChromiumOptions().set_paths(local_port=self.port)
            self.page = Chromium(addr_or_opts=co)

            # 检查是否有已打开的标签页
            for t in self.page.get_tabs():
                if t.title != '新标签页':
                    self.tab = t
                    self.tab.wait.doc_loaded()
                    break
            else:
                self.tab = self.page.latest_tab

            # 检查当前URL是否为登录页面
            current_url = self.tab.url
            if not current_url.startswith(self.TARGET_URL):
                print(f"当前页面不是登录页面，正在跳转到: {self.TARGET_URL}")
                self.tab.get(self.TARGET_URL)
                print("等待页面加载...")
                time.sleep(5)  # 增加等待时间
                self.tab.wait.doc_loaded()  # 等待文档加载完成
            else:
                print("已在登录页面")

            print(f"当前页面标题: {self.tab.title}")
            print(f"当前页面URL: {self.tab.url}")
            return True

        except Exception as e:
            print(f"打开浏览器失败: {e}")
            return False
    
    def input_email(self):
        """输入邮箱账号"""
        try:
            print("正在输入邮箱账号...")

            # 尝试多种选择器查找邮箱输入框
            selectors = [
                '#account_name_text_field',
                'input[can-field="accountName"]',
                'input[type="text"][autocomplete="off"]',
                '.form-textbox-input'
            ]

            email_input = None
            for selector in selectors:
                print(f"尝试选择器: {selector}")
                email_input = self.tab.ele(selector, timeout=5)
                if email_input:
                    print(f"找到邮箱输入框: {selector}")
                    break

            if not email_input:
                print("未找到邮箱输入框，尝试查看页面内容...")
                # 打印页面源码的一部分用于调试
                page_text = self.tab.html[:1000]
                print(f"页面内容片段: {page_text}")
                return False

            # 清空并输入邮箱
            email_input.clear()
            email_input.input(self.email)
            print(f"已输入邮箱: {self.email}")

            # 按回车键
            email_input.input('\n')
            print("已按回车键")

            # 等待2秒
            time.sleep(2)
            return True

        except Exception as e:
            print(f"输入邮箱失败: {e}")
            return False
    
    def input_password(self):
        """输入密码"""
        try:
            print("正在输入密码...")

            # 尝试多种选择器查找密码输入框
            selectors = [
                '#password_text_field',
                'input[type="password"]',
                'input[can-field="password"]'
            ]

            password_input = None
            for selector in selectors:
                print(f"尝试密码选择器: {selector}")
                password_input = self.tab.ele(selector, timeout=5)
                if password_input:
                    print(f"找到密码输入框: {selector}")
                    break

            if not password_input:
                print("未找到密码输入框")
                return False

            # 清空并输入密码
            password_input.clear()
            password_input.input(self.password)
            print("已输入密码")

            return True

        except Exception as e:
            print(f"输入密码失败: {e}")
            return False

    def click_login_button(self):
        """点击登录按钮"""
        try:
            print("正在点击登录按钮...")

            # 尝试多种选择器查找登录按钮
            selectors = [
                '#sign-in',
                'button.signin-v2__buttons-wrapper__button-wrapper__button',
                'button[class*="signin"]',
                'button:contains("登录")'
            ]

            login_button = None
            for selector in selectors:
                print(f"尝试登录按钮选择器: {selector}")
                login_button = self.tab.ele(selector, timeout=5)
                if login_button:
                    print(f"找到登录按钮: {selector}")
                    break

            if not login_button:
                print("未找到登录按钮")
                return False

            # 点击登录按钮
            login_button.click()
            print("已点击登录按钮")

            # 等待页面响应
            time.sleep(3)
            return True

        except Exception as e:
            print(f"点击登录按钮失败: {e}")
            return False
    
    def get_verification_code_from_api(self):
        """从API获取验证码"""
        try:
            print("正在从API获取验证码...")
            
            # 发送API请求
            response = requests.get(self.api_url, timeout=30)
            response.raise_for_status()
            
            # 解析JSON响应
            data = response.json()
            print(f"API响应: {data}")
            
            if data.get('code') == 1 and 'data' in data:
                code_text = data['data'].get('code', '')
                
                # 使用正则表达式提取验证码
                # 匹配6位数字
                match = re.search(r'\b(\d{6})\b', code_text)
                if match:
                    verification_code = match.group(1)
                    print(f"提取到验证码: {verification_code}")
                    return verification_code
                else:
                    print(f"无法从文本中提取验证码: {code_text}")
                    return None
            else:
                print(f"API返回错误: {data}")
                return None
                
        except Exception as e:
            print(f"获取验证码失败: {e}")
            return None
    
    def input_verification_code(self, code):
        """输入验证码"""
        try:
            print(f"正在输入验证码: {code}")
            
            # 查找验证码输入框（第一个）
            code_input = self.tab.ele('css:.form-security-code-input', timeout=10)
            if not code_input:
                print("未找到验证码输入框")
                return False
            
            # 输入验证码（一次性输入所有6位数字）
            code_input.input(code)
            print("已输入验证码")
            
            # 等待验证
            time.sleep(3)
            return True
            
        except Exception as e:
            print(f"输入验证码失败: {e}")
            return False
    
    def wait_for_verification_code_input(self):
        """等待验证码输入框出现并处理验证码"""
        try:
            print("等待验证码输入框出现...")
            
            # 等待验证码输入框出现（最多等待30秒）
            code_input = self.tab.ele('css:.form-security-code-input', timeout=30)
            if not code_input:
                print("验证码输入框未出现，可能不需要验证码或登录失败")
                return True  # 可能不需要验证码
            
            print("检测到验证码输入框")
            
            # 获取验证码
            verification_code = self.get_verification_code_from_api()
            if not verification_code:
                print("获取验证码失败")
                return False
            
            # 输入验证码
            return self.input_verification_code(verification_code)
            
        except Exception as e:
            print(f"处理验证码失败: {e}")
            return False
    
    def check_login_success(self):
        """检查登录是否成功"""
        try:
            print("检查登录状态...")
            
            # 等待页面加载
            time.sleep(5)
            
            current_url = self.tab.url
            print(f"当前URL: {current_url}")
            
            # 如果不再是登录页面，说明登录成功
            if not current_url.startswith("https://account.apple.com/sign-in"):
                print("✅ 登录成功！")
                return True
            else:
                print("❌ 仍在登录页面，登录可能失败")
                return False
                
        except Exception as e:
            print(f"检查登录状态失败: {e}")
            return False
    
    def auto_login(self):
        """执行完整的自动登录流程"""
        print("=== Apple账号自动登录开始 ===")
        
        try:
            # 1. 打开浏览器
            if not self.open_browser():
                return False
            
            # 2. 输入邮箱
            if not self.input_email():
                return False
            
            # 3. 输入密码
            if not self.input_password():
                return False
            
            # 4. 点击登录按钮
            if not self.click_login_button():
                return False
            
            # 5. 处理验证码（如果需要）
            if not self.wait_for_verification_code_input():
                return False
            
            # 6. 检查登录结果
            success = self.check_login_success()
            
            if success:
                print("=== 自动登录完成 ===")
            else:
                print("=== 自动登录失败 ===")
            
            return success
            
        except Exception as e:
            print(f"自动登录过程中出错: {e}")
            return False

def main():
    """主函数"""
    print("Apple账号自动登录工具")
    print("-" * 30)
    
    # 可以修改这里的账号ID来使用不同的账号
    account_id = 1
    
    try:
        # 创建自动登录器
        auto_login = AppleAutoLogin(account_id)
        
        # 执行自动登录
        success = auto_login.auto_login()
        
        if success:
            print("\n🎉 登录成功！浏览器将保持打开状态。")
        else:
            print("\n❌ 登录失败，请检查账号信息或网络连接。")
            
    except Exception as e:
        print(f"\n程序运行出错: {e}")

if __name__ == '__main__':
    main()
